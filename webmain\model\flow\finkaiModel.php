<?php
/**
 * 开票申请模块Model
 * 处理开票申请相关的业务逻辑和数据显示
 */
class flow_finkaiClassModel extends flowModel
{
	/**
	 * 流程数据替换处理
	 * @param array $rs 原始数据记录
	 * @param int $lx 显示类型，0默认,1详情展示，2列表显示
	 * @return array 处理后的数据记录
	 */
	public function flowrsreplace($rs, $lx=0)
	{
		// 处理销售方名称显示
		if(isset($rs['name']) && !empty($rs['name'])){
			$companyId = $rs['name'];
			
			// 如果是数字ID，则获取对应的公司名称
			if(is_numeric($companyId) && $companyId > 0){
				$companyInfo = m('company')->getone($companyId, 'name,nameen');
				if($companyInfo){
					// 优先使用英文名称，如果没有则使用中文名称
					$displayName = !empty($companyInfo['nameen']) ? $companyInfo['nameen'] : $companyInfo['name'];
					$rs['name'] = $displayName;
					
					// 如果是详情展示，可以添加更多信息或链接
					if($lx == 1){
						// 保持简单显示，不添加链接
						$rs['name'] = $displayName;
					}
				}
			}
		}
		
		// 处理开票项目显示（仅在列表页面显示）
		if($lx == 2 && isset($rs['num'])){
			$numValue = $rs['num'];

			if(empty($numValue) || $numValue == '0'){
				// 没有关联项目
				$rs['num'] = '<font color=#aaaaaa>无关联</font>';
			} else {
				// 有关联项目，判断类型并生成链接
				$displayText = '';
				$linkUrl = '';

				if((int)$numValue < 0) {
					// 负数表示销售单
					$salesId = abs((int)$numValue);
					$salesInfo = $this->db->getone('[Q]goodm', '`id`='.$salesId.' AND `type`=2', 'num');
					if($salesInfo && !empty($salesInfo['num'])) {
						$displayText = $salesInfo['num'];
						$linkUrl = $this->getxiangurl('custxiao', $salesId, 'auto');
					}
				} else if((int)$numValue > 0) {
					// 正数，需要判断是合同还是电子服务单
					// 先尝试作为合同查询
					$contractInfo = m('custract')->getone((int)$numValue, 'num');
					if($contractInfo && !empty($contractInfo['num'])) {
						$displayText = $contractInfo['num'];
						$linkUrl = $this->getxiangurl('custract', (int)$numValue, 'auto');
					} else {
						// 尝试作为电子服务单查询
						$electInfo = $this->db->getone('[Q]goodm', '`id`='.(int)$numValue.' AND `type`=6', 'num');
						if($electInfo && !empty($electInfo['num'])) {
							$displayText = $electInfo['num'];
							$linkUrl = $this->getxiangurl('electwork', (int)$numValue, 'auto');
						}
					}
				}

				// 生成显示内容
				if(!empty($displayText)) {
					if(!empty($linkUrl)) {
						$rs['num'] = '<a href="'.$linkUrl.'" target="_blank">'.$displayText.'</a>';
					} else {
						$rs['num'] = $displayText;
					}
				} else {
					$rs['num'] = '<font color=#ff0000>关联错误</font>';
				}
			}
		}
		
		return $rs;
	}
	
	/**
	 * 删除单据时的处理
	 * @param string $sm 删除说明
	 */
	protected function flowdeletebill($sm)
	{
		$this->resetElectworkReceiptStatus();
	}
	
	/**
	 * 作废单据时的处理
	 * @param string $sm 作废说明
	 */
	protected function flowzuofeibill($sm)
	{
		$this->resetElectworkReceiptStatus();
	}

	/**
	 * 重置电子服务单的开票状态
	 * 将关联的电子服务单的receipt字段从3改回1
	 */
	private function resetElectworkReceiptStatus()
	{
		// 获取当前开票申请的关联项目编号
		$numValue = isset($this->rs['num']) ? $this->rs['num'] : '';

		if(!empty($numValue) && is_numeric($numValue) && (int)$numValue > 0){
			$electId = (int)$numValue;

			// 检查是否为电子服务单（type=6）且当前receipt=3
			$electInfo = $this->db->getone('[Q]goodm', '`id`='.$electId.' AND `type`=6 AND `receipt`=3', 'id');
			if($electInfo){
				// 将电子服务单的receipt字段改回1（待开票状态）
				m('goodm')->update(array('receipt' => '1'), $electId);
			}
		}
	}
} 